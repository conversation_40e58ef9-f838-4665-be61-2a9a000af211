using System;

namespace TCP通讯.Core.Interfaces
{
    /// <summary>
    /// 键盘输入服务接口
    /// </summary>
    public interface IKeyboardService
    {
        /// <summary>
        /// 键盘输入完成事件
        /// </summary>
        event EventHandler<KeyboardInputEventArgs> InputCompleted;

        /// <summary>
        /// 键盘输入失败事件
        /// </summary>
        event EventHandler<KeyboardInputErrorEventArgs> InputFailed;

        /// <summary>
        /// 发送文本输入
        /// </summary>
        /// <param name="text">要输入的文本</param>
        /// <param name="addEnter">是否添加回车符</param>
        /// <param name="convertToUpper">是否转换为大写</param>
        /// <returns>是否成功发送</returns>
        bool SendTextInput(string text, bool addEnter = true, bool convertToUpper = true);

        /// <summary>
        /// 发送特殊按键
        /// </summary>
        /// <param name="key">按键代码</param>
        /// <returns>是否成功发送</returns>
        bool SendKeyPress(VirtualKey key);

        /// <summary>
        /// 发送组合键
        /// </summary>
        /// <param name="modifierKeys">修饰键</param>
        /// <param name="key">主按键</param>
        /// <returns>是否成功发送</returns>
        bool SendKeyCombo(ModifierKeys modifierKeys, VirtualKey key);

        /// <summary>
        /// 检查键盘输入服务是否可用
        /// </summary>
        bool IsAvailable { get; }

        /// <summary>
        /// 设置输入延迟（毫秒）
        /// </summary>
        int InputDelay { get; set; }
    }

    /// <summary>
    /// 键盘输入事件参数
    /// </summary>
    public class KeyboardInputEventArgs : EventArgs
    {
        public string InputText { get; set; }
        public DateTime InputTime { get; set; } = DateTime.Now;
        public int CharacterCount { get; set; }
    }

    /// <summary>
    /// 键盘输入错误事件参数
    /// </summary>
    public class KeyboardInputErrorEventArgs : EventArgs
    {
        public string InputText { get; set; }
        public Exception Exception { get; set; }
        public string ErrorMessage { get; set; }
        public DateTime ErrorTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 虚拟按键枚举
    /// </summary>
    public enum VirtualKey
    {
        Enter = 0x0D,
        Tab = 0x09,
        Escape = 0x1B,
        Space = 0x20,
        Backspace = 0x08,
        Delete = 0x2E,
        Home = 0x24,
        End = 0x23,
        PageUp = 0x21,
        PageDown = 0x22,
        ArrowLeft = 0x25,
        ArrowUp = 0x26,
        ArrowRight = 0x27,
        ArrowDown = 0x28,
        F1 = 0x70,
        F2 = 0x71,
        F3 = 0x72,
        F4 = 0x73,
        F5 = 0x74,
        F6 = 0x75,
        F7 = 0x76,
        F8 = 0x77,
        F9 = 0x78,
        F10 = 0x79,
        F11 = 0x7A,
        F12 = 0x7B
    }

    /// <summary>
    /// 修饰键枚举
    /// </summary>
    [Flags]
    public enum ModifierKeys
    {
        None = 0,
        Alt = 1,
        Control = 2,
        Shift = 4,
        Windows = 8
    }
}
