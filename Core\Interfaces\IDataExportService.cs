using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TCP通讯.Core.Models;

namespace TCP通讯.Core.Interfaces
{
    /// <summary>
    /// 数据导出服务接口
    /// </summary>
    public interface IDataExportService
    {
        /// <summary>
        /// 导出完成事件
        /// </summary>
        event EventHandler<ExportCompletedEventArgs> ExportCompleted;

        /// <summary>
        /// 导出进度事件
        /// </summary>
        event EventHandler<ExportProgressEventArgs> ExportProgress;

        /// <summary>
        /// 导出数据到CSV文件
        /// </summary>
        /// <param name="data">要导出的数据</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="options">导出选项</param>
        Task<bool> ExportToCsvAsync(IEnumerable<DataRecord> data, string filePath, ExportOptions options = null);

        /// <summary>
        /// 导出数据到Excel文件
        /// </summary>
        /// <param name="data">要导出的数据</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="options">导出选项</param>
        Task<bool> ExportToExcelAsync(IEnumerable<DataRecord> data, string filePath, ExportOptions options = null);

        /// <summary>
        /// 导出数据到JSON文件
        /// </summary>
        /// <param name="data">要导出的数据</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="options">导出选项</param>
        Task<bool> ExportToJsonAsync(IEnumerable<DataRecord> data, string filePath, ExportOptions options = null);

        /// <summary>
        /// 导出数据到XML文件
        /// </summary>
        /// <param name="data">要导出的数据</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="options">导出选项</param>
        Task<bool> ExportToXmlAsync(IEnumerable<DataRecord> data, string filePath, ExportOptions options = null);

        /// <summary>
        /// 导出性能指标
        /// </summary>
        /// <param name="metrics">性能指标数据</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="format">导出格式</param>
        Task<bool> ExportPerformanceMetricsAsync(IEnumerable<PerformanceMetrics> metrics, string filePath, ExportFormat format);

        /// <summary>
        /// 获取支持的导出格式
        /// </summary>
        IEnumerable<ExportFormat> GetSupportedFormats();

        /// <summary>
        /// 验证文件路径
        /// </summary>
        bool ValidateFilePath(string filePath, ExportFormat format);

        /// <summary>
        /// 取消当前导出操作
        /// </summary>
        void CancelExport();

        /// <summary>
        /// 是否正在导出
        /// </summary>
        bool IsExporting { get; }
    }

    /// <summary>
    /// 导出选项
    /// </summary>
    public class ExportOptions
    {
        /// <summary>
        /// 是否包含标题行
        /// </summary>
        public bool IncludeHeaders { get; set; } = true;

        /// <summary>
        /// 日期时间格式
        /// </summary>
        public string DateTimeFormat { get; set; } = "yyyy-MM-dd HH:mm:ss";

        /// <summary>
        /// 字符编码
        /// </summary>
        public string Encoding { get; set; } = "UTF-8";

        /// <summary>
        /// CSV分隔符
        /// </summary>
        public string CsvSeparator { get; set; } = ",";

        /// <summary>
        /// 是否压缩输出
        /// </summary>
        public bool Compress { get; set; } = false;

        /// <summary>
        /// 最大记录数（0表示无限制）
        /// </summary>
        public int MaxRecords { get; set; } = 0;

        /// <summary>
        /// 过滤条件
        /// </summary>
        public Func<DataRecord, bool> Filter { get; set; }

        /// <summary>
        /// 自定义字段映射
        /// </summary>
        public Dictionary<string, string> FieldMapping { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// 是否包含自定义字段
        /// </summary>
        public bool IncludeCustomFields { get; set; } = true;
    }

    /// <summary>
    /// 导出格式
    /// </summary>
    public enum ExportFormat
    {
        Csv,
        Excel,
        Json,
        Xml,
        Pdf,
        Html
    }

    /// <summary>
    /// 导出完成事件参数
    /// </summary>
    public class ExportCompletedEventArgs : EventArgs
    {
        public bool Success { get; set; }
        public string FilePath { get; set; }
        public ExportFormat Format { get; set; }
        public int RecordCount { get; set; }
        public TimeSpan Duration { get; set; }
        public Exception Exception { get; set; }
        public string Message { get; set; }
    }

    /// <summary>
    /// 导出进度事件参数
    /// </summary>
    public class ExportProgressEventArgs : EventArgs
    {
        public int ProcessedRecords { get; set; }
        public int TotalRecords { get; set; }
        public double ProgressPercentage => TotalRecords > 0 ? (double)ProcessedRecords / TotalRecords * 100 : 0;
        public string CurrentOperation { get; set; }
        public TimeSpan ElapsedTime { get; set; }
        public TimeSpan EstimatedRemainingTime { get; set; }
    }
}
