using System;
using System.Windows.Forms;

namespace TCP通讯.Core.Interfaces
{
    /// <summary>
    /// 消息服务接口 - 使用AntdUI组件
    /// </summary>
    public interface IMessageService
    {
        /// <summary>
        /// 显示成功消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="form">所属窗体</param>
        void ShowSuccess(string message, Form form = null);

        /// <summary>
        /// 显示错误消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="form">所属窗体</param>
        void ShowError(string message, Form form = null);

        /// <summary>
        /// 显示警告消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="form">所属窗体</param>
        void ShowWarning(string message, Form form = null);

        /// <summary>
        /// 显示信息消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="form">所属窗体</param>
        void ShowInfo(string message, Form form = null);

        /// <summary>
        /// 显示通知
        /// </summary>
        /// <param name="title">标题</param>
        /// <param name="message">消息内容</param>
        /// <param name="type">通知类型</param>
        /// <param name="form">所属窗体</param>
        /// <param name="autoClose">自动关闭时间(秒)，0表示不自动关闭</param>
        void ShowNotification(string title, string message, NotificationType type = NotificationType.Info, Form form = null, int autoClose = 6);

        /// <summary>
        /// 显示确认对话框
        /// </summary>
        /// <param name="title">标题</param>
        /// <param name="message">消息内容</param>
        /// <param name="onConfirm">确认回调</param>
        /// <param name="onCancel">取消回调</param>
        /// <param name="form">所属窗体</param>
        void ShowConfirm(string title, string message, Action onConfirm = null, Action onCancel = null, Form form = null);

        /// <summary>
        /// 关闭所有消息
        /// </summary>
        void CloseAllMessages();

        /// <summary>
        /// 关闭所有通知
        /// </summary>
        void CloseAllNotifications();
    }
}
