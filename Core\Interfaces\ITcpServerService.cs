using System;
using System.Threading;
using System.Threading.Tasks;

namespace TCP通讯.Core.Interfaces
{
    /// <summary>
    /// TCP服务器服务接口
    /// </summary>
    public interface ITcpServerService : IDisposable
    {
        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        event EventHandler<ConnectionStatusEventArgs> ConnectionStatusChanged;
        
        /// <summary>
        /// 数据接收事件
        /// </summary>
        event EventHandler<DataReceivedEventArgs> DataReceived;

        /// <summary>
        /// 启动TCP服务器
        /// </summary>
        /// <param name="serverConfig">服务器配置</param>
        /// <param name="cancellationToken">取消令牌</param>
        Task StartAsync(TCP通讯.Core.Models.ServerConfiguration serverConfig, CancellationToken cancellationToken = default);

        /// <summary>
        /// 停止TCP服务器
        /// </summary>
        Task StopAsync();

        /// <summary>
        /// 获取服务器状态
        /// </summary>
        bool IsRunning { get; }

        /// <summary>
        /// 获取活动连接数
        /// </summary>
        int ActiveConnectionCount { get; }
    }

    /// <summary>
    /// 连接状态事件参数
    /// </summary>
    public class ConnectionStatusEventArgs : EventArgs
    {
        public string ServerName { get; set; }
        public ConnectionStatus Status { get; set; }
        public string Message { get; set; }
        public Exception Exception { get; set; }
    }

    /// <summary>
    /// 数据接收事件参数
    /// </summary>
    public class DataReceivedEventArgs : EventArgs
    {
        public string ServerName { get; set; }
        public string Data { get; set; }
        public DateTime Timestamp { get; set; }
        public string ClientEndpoint { get; set; }
    }

    /// <summary>
    /// 连接状态枚举
    /// </summary>
    public enum ConnectionStatus
    {
        Waiting,
        Connected,
        Disconnected,
        Error
    }
}
