using System;

namespace TCP通讯.Core.Interfaces
{
    /// <summary>
    /// 系统服务接口
    /// </summary>
    public interface ISystemService
    {
        /// <summary>
        /// 系统事件
        /// </summary>
        event EventHandler<SystemEventArgs> SystemEvent;

        /// <summary>
        /// 设置开机自启
        /// </summary>
        /// <param name="enable">是否启用</param>
        /// <returns>是否成功</returns>
        bool SetAutoStart(bool enable);

        /// <summary>
        /// 检查是否已设置开机自启
        /// </summary>
        /// <returns>是否已设置</returns>
        bool IsAutoStartEnabled();

        /// <summary>
        /// 创建桌面快捷方式
        /// </summary>
        /// <param name="shortcutName">快捷方式名称</param>
        /// <param name="description">描述</param>
        /// <returns>是否成功</returns>
        bool CreateDesktopShortcut(string shortcutName = null, string description = null);

        /// <summary>
        /// 删除桌面快捷方式
        /// </summary>
        /// <param name="shortcutName">快捷方式名称</param>
        /// <returns>是否成功</returns>
        bool RemoveDesktopShortcut(string shortcutName = null);

        /// <summary>
        /// 获取应用程序信息
        /// </summary>
        ApplicationInfo GetApplicationInfo();

        /// <summary>
        /// 检查是否有管理员权限
        /// </summary>
        bool IsRunningAsAdministrator();

        /// <summary>
        /// 重启应用程序
        /// </summary>
        /// <param name="asAdministrator">是否以管理员身份重启</param>
        void RestartApplication(bool asAdministrator = false);
    }

    /// <summary>
    /// 系统事件参数
    /// </summary>
    public class SystemEventArgs : EventArgs
    {
        public SystemEventType EventType { get; set; }
        public string Message { get; set; }
        public bool Success { get; set; }
        public Exception Exception { get; set; }
    }

    /// <summary>
    /// 系统事件类型
    /// </summary>
    public enum SystemEventType
    {
        AutoStartEnabled,
        AutoStartDisabled,
        ShortcutCreated,
        ShortcutRemoved,
        ApplicationRestarted,
        PermissionChanged
    }

    /// <summary>
    /// 应用程序信息
    /// </summary>
    public class ApplicationInfo
    {
        public string Name { get; set; }
        public string Version { get; set; }
        public string ExecutablePath { get; set; }
        public string WorkingDirectory { get; set; }
        public DateTime StartTime { get; set; }
        public bool IsRunningAsAdministrator { get; set; }
        public string ProcessId { get; set; }
        public long MemoryUsage { get; set; }
    }
}
