using System.Net;

namespace TCP通讯.Core.Models
{
    /// <summary>
    /// 服务器配置模型
    /// </summary>
    public class ServerConfiguration
    {
        /// <summary>
        /// 服务器名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// IP地址
        /// </summary>
        public IPAddress IPAddress { get; set; }

        /// <summary>
        /// 端口号
        /// </summary>
        public int Port { get; set; }

        /// <summary>
        /// 监听队列大小
        /// </summary>
        public int BacklogSize { get; set; } = 10;

        /// <summary>
        /// 接收缓冲区大小
        /// </summary>
        public int BufferSize { get; set; } = 1024;

        /// <summary>
        /// 最大连接数
        /// </summary>
        public int MaxConnections { get; set; } = 100;

        /// <summary>
        /// 连接超时时间（毫秒）
        /// </summary>
        public int ConnectionTimeout { get; set; } = 30000;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 验证配置是否有效
        /// </summary>
        public bool IsValid()
        {
            return IPAddress != null && 
                   Port > 0 && Port <= 65535 && 
                   BacklogSize > 0 && 
                   BufferSize > 0 && 
                   MaxConnections > 0 &&
                   ConnectionTimeout > 0;
        }

        public override string ToString()
        {
            return $"{Name} - {IPAddress}:{Port}";
        }
    }
}
