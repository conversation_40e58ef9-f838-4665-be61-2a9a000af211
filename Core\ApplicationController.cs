using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using TCP通讯.Core.Interfaces;
using TCP通讯.Core.Models;
using TCP通讯.Core.Services;
using TCP通讯.Infrastructure.Configuration;
using TCP通讯.Infrastructure.Logging;
using TCP通讯.Common;
using TCP通讯.Common.Exceptions;

namespace TCP通讯.Core
{
    /// <summary>
    /// 应用程序主控制器
    /// </summary>
    public class ApplicationController : IDisposable
    {
        private readonly ILogger _logger;
        private readonly IConfigurationManager _configManager;
        private readonly ITcpServerService _tcpServerService;
        private readonly IDataProcessingService _dataProcessingService;
        private readonly IKeyboardService _keyboardService;
        private readonly IMessageService _messageService;
        
        private AppConfiguration _appConfig;
        private TCP通讯.Core.Models.ServerConfiguration[] _serverConfigs;
        private TCP通讯.Infrastructure.Configuration.DataFilterConfiguration _filterConfig;
        private bool _isRunning;
        private bool _disposed;

        // 事件
        public event EventHandler<ConnectionStatusEventArgs> ConnectionStatusChanged;
        public event EventHandler<ProcessedDataEventArgs> DataProcessed;
        public event EventHandler<DataFilteredEventArgs> DataFiltered;
        public event EventHandler<KeyboardInputEventArgs> KeyboardInputCompleted;
        public event EventHandler<ApplicationErrorEventArgs> ApplicationError;

        public bool IsRunning => _isRunning;
        public AppConfiguration Configuration => _appConfig;
        public DataProcessingStatistics Statistics => _dataProcessingService?.GetStatistics();
        public ILogger Logger => _logger;
        public IMessageService MessageService => _messageService;

        public ApplicationController()
        {
            try
            {
                // 初始化日志服务
                _logger = new FileLogger();
                _logger.LogInfo("应用程序控制器初始化开始");

                // 初始化配置管理器
                _configManager = new ConfigurationManager(_logger);
                
                // 加载配置
                LoadConfigurations();

                // 初始化服务
                _tcpServerService = new TcpServerService(_logger);
                _dataProcessingService = new DataProcessingService(_logger);
                _keyboardService = new KeyboardService(_logger);
                _messageService = new AntdMessageService(_logger);

                // 订阅事件
                SubscribeToEvents();

                _logger.LogInfo("应用程序控制器初始化完成");
            }
            catch (Exception ex)
            {
                _logger?.LogError("应用程序控制器初始化失败", ex);
                throw new ApplicationException("应用程序初始化失败", ex);
            }
        }

        /// <summary>
        /// 启动应用程序
        /// </summary>
        public async Task StartAsync()
        {
            try
            {
                if (_isRunning)
                {
                    _logger.LogWarning("应用程序已在运行中");
                    return;
                }

                _logger.LogInfo("启动应用程序");

                // 启动启用的服务器
                var enabledServers = _serverConfigs?.Where(s => s.IsEnabled).ToArray() ?? new ServerConfiguration[0];
                
                foreach (var serverConfig in enabledServers)
                {
                    if (serverConfig.IsValid())
                    {
                        await _tcpServerService.StartAsync(serverConfig);
                    }
                    else
                    {
                        _logger.LogWarning($"服务器配置无效，跳过启动: {serverConfig}");
                    }
                }

                _isRunning = true;
                _logger.LogInfo($"应用程序启动完成，共启动 {enabledServers.Length} 个服务器");
            }
            catch (Exception ex)
            {
                _logger.LogError("启动应用程序失败", ex);
                OnApplicationError("启动失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 停止应用程序
        /// </summary>
        public async Task StopAsync()
        {
            try
            {
                if (!_isRunning)
                {
                    _logger.LogWarning("应用程序未在运行中");
                    return;
                }

                _logger.LogInfo("停止应用程序");

                await _tcpServerService.StopAsync();
                
                _isRunning = false;
                _logger.LogInfo("应用程序已停止");
            }
            catch (Exception ex)
            {
                _logger.LogError("停止应用程序失败", ex);
                OnApplicationError("停止失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 重新加载配置
        /// </summary>
        public void ReloadConfiguration()
        {
            try
            {
                _logger.LogInfo("重新加载配置");
                LoadConfigurations();
                _logger.LogInfo("配置重新加载完成");
            }
            catch (Exception ex)
            {
                _logger.LogError("重新加载配置失败", ex);
                OnApplicationError("配置加载失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        public void SaveConfiguration()
        {
            try
            {
                _logger.LogInfo("保存配置");
                
                _configManager.SaveConfiguration(_appConfig);
                _configManager.SaveServerConfigurations(_serverConfigs);
                _configManager.SaveDataFilterConfiguration(_filterConfig);
                
                _logger.LogInfo("配置保存完成");
            }
            catch (Exception ex)
            {
                _logger.LogError("保存配置失败", ex);
                OnApplicationError("配置保存失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 更新服务器配置
        /// </summary>
        public void UpdateServerConfiguration(TCP通讯.Core.Models.ServerConfiguration[] configs)
        {
            if (configs == null)
                throw new ArgumentNullException(nameof(configs));

            _serverConfigs = configs;
            _configManager.SaveServerConfigurations(_serverConfigs);
            _logger.LogInfo($"服务器配置已更新，共 {configs.Length} 个服务器");
        }

        /// <summary>
        /// 更新数据过滤配置
        /// </summary>
        public void UpdateDataFilterConfiguration(TCP通讯.Infrastructure.Configuration.DataFilterConfiguration config)
        {
            if (config == null)
                throw new ArgumentNullException(nameof(config));

            _filterConfig = config;
            _configManager.SaveDataFilterConfiguration(_filterConfig);
            _logger.LogInfo("数据过滤配置已更新");
        }

        /// <summary>
        /// 获取服务器配置
        /// </summary>
        public TCP通讯.Core.Models.ServerConfiguration[] GetServerConfigurations()
        {
            return _serverConfigs?.ToArray() ?? new TCP通讯.Core.Models.ServerConfiguration[0];
        }

        /// <summary>
        /// 获取数据过滤配置
        /// </summary>
        public TCP通讯.Infrastructure.Configuration.DataFilterConfiguration GetDataFilterConfiguration()
        {
            return _filterConfig;
        }

        private void LoadConfigurations()
        {
            _appConfig = _configManager.LoadConfiguration();
            _serverConfigs = _configManager.GetServerConfigurations();
            _filterConfig = _configManager.GetDataFilterConfiguration();
            
            _logger.LogInfo("配置加载完成");
        }

        /// <summary>
        /// 设置消息服务的默认窗体
        /// </summary>
        /// <param name="form">默认窗体</param>
        public void SetMessageDefaultForm(System.Windows.Forms.Form form)
        {
            if (_messageService is AntdMessageService antdService)
            {
                antdService.SetDefaultForm(form);
            }
        }

        private void SubscribeToEvents()
        {
            // TCP服务器事件
            _tcpServerService.ConnectionStatusChanged += OnConnectionStatusChanged;
            _tcpServerService.DataReceived += OnDataReceived;

            // 数据处理事件
            _dataProcessingService.DataProcessed += OnDataProcessed;
            _dataProcessingService.DataFiltered += OnDataFiltered;

            // 键盘输入事件
            _keyboardService.InputCompleted += OnKeyboardInputCompleted;
            _keyboardService.InputFailed += OnKeyboardInputFailed;

            // 配置变更事件
            _configManager.ConfigurationChanged += OnConfigurationChanged;
        }

        private void OnConnectionStatusChanged(object sender, ConnectionStatusEventArgs e)
        {
            _logger.LogInfo($"连接状态变更: {e.ServerName} - {e.Status} - {e.Message}");
            ConnectionStatusChanged?.Invoke(this, e);
        }

        private void OnDataReceived(object sender, DataReceivedEventArgs e)
        {
            try
            {
                _logger.LogDebug($"接收到数据: {e.ServerName} - {e.Data}");

                var dataRecord = new DataRecord(e.Data, e.ClientEndpoint, e.ServerName)
                {
                    Timestamp = e.Timestamp
                };

                var processedData = _dataProcessingService.ProcessData(dataRecord, _filterConfig);
                
                if (!string.IsNullOrEmpty(processedData))
                {
                    // 发送键盘输入
                    _keyboardService.SendTextInput(processedData);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"处理接收数据时发生错误: {e.Data}", ex);
                OnApplicationError("数据处理错误", ex);
            }
        }

        private void OnDataProcessed(object sender, ProcessedDataEventArgs e)
        {
            _logger.LogInfo($"数据处理完成: {e.OriginalData} -> {e.ProcessedData}");
            DataProcessed?.Invoke(this, e);
        }

        private void OnDataFiltered(object sender, DataFilteredEventArgs e)
        {
            _logger.LogDebug($"数据被过滤: {e.Data} - {e.Reason}");
            DataFiltered?.Invoke(this, e);
        }

        private void OnKeyboardInputCompleted(object sender, KeyboardInputEventArgs e)
        {
            _logger.LogInfo($"键盘输入完成: {e.CharacterCount} 个字符");
            KeyboardInputCompleted?.Invoke(this, e);
        }

        private void OnKeyboardInputFailed(object sender, KeyboardInputErrorEventArgs e)
        {
            _logger.LogError($"键盘输入失败: {e.InputText}", e.Exception);
            OnApplicationError("键盘输入失败", e.Exception);
        }

        private void OnConfigurationChanged(object sender, ConfigurationChangedEventArgs e)
        {
            _logger.LogInfo($"配置已变更: {e.ConfigurationType}");
        }

        private void OnApplicationError(string message, Exception exception)
        {
            ApplicationError?.Invoke(this, new ApplicationErrorEventArgs
            {
                Message = message,
                Exception = exception,
                Timestamp = DateTime.Now
            });
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    StopAsync().Wait(5000);
                    
                    _tcpServerService?.Dispose();
                    (_logger as IDisposable)?.Dispose();
                }
                catch (Exception ex)
                {
                    // 忽略释放过程中的错误
                    System.Diagnostics.Debug.WriteLine($"释放资源时发生错误: {ex.Message}");
                }
                
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// 应用程序错误事件参数
    /// </summary>
    public class ApplicationErrorEventArgs : EventArgs
    {
        public string Message { get; set; }
        public Exception Exception { get; set; }
        public DateTime Timestamp { get; set; }
    }
}
