using System;
using System.Runtime.InteropServices;
using System.Threading;
using TCP通讯.Core.Interfaces;
using TCP通讯.Infrastructure.Logging;

namespace TCP通讯.Core.Services
{
    /// <summary>
    /// 键盘输入服务实现
    /// </summary>
    public class KeyboardService : IKeyboardService
    {
        private readonly ILogger _logger;
        private int _inputDelay = 10; // 默认10毫秒延迟

        public event EventHandler<KeyboardInputEventArgs> InputCompleted;
        public event EventHandler<KeyboardInputErrorEventArgs> InputFailed;

        public bool IsAvailable => Environment.OSVersion.Platform == PlatformID.Win32NT;
        
        public int InputDelay 
        { 
            get => _inputDelay; 
            set => _inputDelay = Math.Max(0, value); 
        }

        public KeyboardService(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public bool SendTextInput(string text, bool addEnter = true, bool convertToUpper = true)
        {
            if (string.IsNullOrEmpty(text))
                return false;

            if (!IsAvailable)
            {
                var error = "键盘输入服务在当前平台不可用";
                _logger.LogError(error);
                OnInputFailed(text, new PlatformNotSupportedException(error), error);
                return false;
            }

            try
            {
                var processedText = convertToUpper ? text.ToUpperInvariant() : text;
                
                if (addEnter && !processedText.EndsWith("\r") && !processedText.EndsWith("\n"))
                {
                    processedText += "\r";
                }

                _logger.LogDebug($"发送键盘输入: {processedText}");

                var inputs = CreateInputsForText(processedText);
                var result = SendInput((uint)inputs.Length, inputs, Marshal.SizeOf(typeof(INPUT)));

                if (result == 0)
                {
                    var error = "SendInput 调用失败";
                    _logger.LogError(error);
                    OnInputFailed(text, new InvalidOperationException(error), error);
                    return false;
                }

                OnInputCompleted(processedText);
                _logger.LogInfo($"键盘输入发送成功: {processedText.Length} 个字符");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"发送键盘输入时发生错误: {text}", ex);
                OnInputFailed(text, ex, ex.Message);
                return false;
            }
        }

        public bool SendKeyPress(VirtualKey key)
        {
            if (!IsAvailable)
                return false;

            try
            {
                var inputs = new INPUT[2];
                
                // 按下键
                inputs[0] = CreateKeyInput((ushort)key, false);
                // 释放键
                inputs[1] = CreateKeyInput((ushort)key, true);

                var result = SendInput(2, inputs, Marshal.SizeOf(typeof(INPUT)));
                
                if (_inputDelay > 0)
                    Thread.Sleep(_inputDelay);

                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError($"发送按键时发生错误: {key}", ex);
                return false;
            }
        }

        public bool SendKeyCombo(ModifierKeys modifierKeys, VirtualKey key)
        {
            if (!IsAvailable)
                return false;

            try
            {
                var inputList = new System.Collections.Generic.List<INPUT>();

                // 按下修饰键
                if (modifierKeys.HasFlag(ModifierKeys.Control))
                    inputList.Add(CreateKeyInput(0x11, false)); // VK_CONTROL
                if (modifierKeys.HasFlag(ModifierKeys.Alt))
                    inputList.Add(CreateKeyInput(0x12, false)); // VK_MENU
                if (modifierKeys.HasFlag(ModifierKeys.Shift))
                    inputList.Add(CreateKeyInput(0x10, false)); // VK_SHIFT
                if (modifierKeys.HasFlag(ModifierKeys.Windows))
                    inputList.Add(CreateKeyInput(0x5B, false)); // VK_LWIN

                // 按下主键
                inputList.Add(CreateKeyInput((ushort)key, false));
                // 释放主键
                inputList.Add(CreateKeyInput((ushort)key, true));

                // 释放修饰键（逆序）
                if (modifierKeys.HasFlag(ModifierKeys.Windows))
                    inputList.Add(CreateKeyInput(0x5B, true));
                if (modifierKeys.HasFlag(ModifierKeys.Shift))
                    inputList.Add(CreateKeyInput(0x10, true));
                if (modifierKeys.HasFlag(ModifierKeys.Alt))
                    inputList.Add(CreateKeyInput(0x12, true));
                if (modifierKeys.HasFlag(ModifierKeys.Control))
                    inputList.Add(CreateKeyInput(0x11, true));

                var inputs = inputList.ToArray();
                var result = SendInput((uint)inputs.Length, inputs, Marshal.SizeOf(typeof(INPUT)));

                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError($"发送组合键时发生错误: {modifierKeys} + {key}", ex);
                return false;
            }
        }

        private INPUT[] CreateInputsForText(string text)
        {
            var inputs = new INPUT[text.Length * 2]; // 每个字符需要按下和释放两个事件
            var index = 0;

            foreach (char ch in text)
            {
                // 按下键
                inputs[index] = new INPUT
                {
                    type = INPUT_KEYBOARD,
                    mkhi = new MOUSEKEYBDHARDWAREINPUT
                    {
                        ki = new KEYBDINPUT
                        {
                            wVk = 0,
                            wScan = (ushort)ch,
                            dwFlags = KEYEVENTF_UNICODE,
                            time = 0,
                            dwExtraInfo = IntPtr.Zero
                        }
                    }
                };

                // 释放键
                inputs[index + 1] = new INPUT
                {
                    type = INPUT_KEYBOARD,
                    mkhi = new MOUSEKEYBDHARDWAREINPUT
                    {
                        ki = new KEYBDINPUT
                        {
                            wVk = 0,
                            wScan = (ushort)ch,
                            dwFlags = KEYEVENTF_UNICODE | KEYEVENTF_KEYUP,
                            time = 0,
                            dwExtraInfo = IntPtr.Zero
                        }
                    }
                };

                index += 2;

                // 添加延迟
                if (_inputDelay > 0)
                    Thread.Sleep(_inputDelay);
            }

            return inputs;
        }

        private INPUT CreateKeyInput(ushort virtualKey, bool keyUp)
        {
            return new INPUT
            {
                type = INPUT_KEYBOARD,
                mkhi = new MOUSEKEYBDHARDWAREINPUT
                {
                    ki = new KEYBDINPUT
                    {
                        wVk = virtualKey,
                        wScan = 0,
                        dwFlags = keyUp ? KEYEVENTF_KEYUP : 0,
                        time = 0,
                        dwExtraInfo = IntPtr.Zero
                    }
                }
            };
        }

        private void OnInputCompleted(string inputText)
        {
            InputCompleted?.Invoke(this, new KeyboardInputEventArgs
            {
                InputText = inputText,
                CharacterCount = inputText?.Length ?? 0
            });
        }

        private void OnInputFailed(string inputText, Exception exception, string errorMessage)
        {
            InputFailed?.Invoke(this, new KeyboardInputErrorEventArgs
            {
                InputText = inputText,
                Exception = exception,
                ErrorMessage = errorMessage
            });
        }

        #region P/Invoke Declarations

        [DllImport("user32.dll", SetLastError = true)]
        private static extern uint SendInput(uint nInputs, [In] INPUT[] pInputs, int cbSize);

        [StructLayout(LayoutKind.Sequential)]
        private struct INPUT
        {
            public uint type;
            public MOUSEKEYBDHARDWAREINPUT mkhi;
        }

        [StructLayout(LayoutKind.Explicit)]
        private struct MOUSEKEYBDHARDWAREINPUT
        {
            [FieldOffset(0)]
            public MOUSEINPUT mi;
            [FieldOffset(0)]
            public KEYBDINPUT ki;
            [FieldOffset(0)]
            public HARDWAREINPUT hi;
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct KEYBDINPUT
        {
            public ushort wVk;
            public ushort wScan;
            public uint dwFlags;
            public uint time;
            public IntPtr dwExtraInfo;
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct MOUSEINPUT
        {
            public int dx;
            public int dy;
            public uint mouseData;
            public uint dwFlags;
            public uint time;
            public IntPtr dwExtraInfo;
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct HARDWAREINPUT
        {
            public uint uMsg;
            public ushort wParamL;
            public ushort wParamH;
        }

        private const uint INPUT_KEYBOARD = 1;
        private const uint KEYEVENTF_KEYUP = 0x0002;
        private const uint KEYEVENTF_UNICODE = 0x0004;

        #endregion
    }
}
