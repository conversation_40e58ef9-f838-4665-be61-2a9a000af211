using System;

namespace TCP通讯.Core.Models
{
    /// <summary>
    /// 数据记录模型
    /// </summary>
    public class DataRecord
    {
        /// <summary>
        /// 数据内容
        /// </summary>
        public string Data { get; set; }

        /// <summary>
        /// 接收时间
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 客户端终结点
        /// </summary>
        public string ClientEndpoint { get; set; }

        /// <summary>
        /// 服务器名称
        /// </summary>
        public string ServerName { get; set; }

        /// <summary>
        /// 数据长度
        /// </summary>
        public int Length => Data?.Length ?? 0;

        /// <summary>
        /// 是否为空数据
        /// </summary>
        public bool IsEmpty => string.IsNullOrWhiteSpace(Data);

        public DataRecord()
        {
            Timestamp = DateTime.Now;
        }

        public DataRecord(string data, string clientEndpoint = null, string serverName = null) : this()
        {
            Data = data;
            ClientEndpoint = clientEndpoint;
            ServerName = serverName;
        }

        public override string ToString()
        {
            return $"[{Timestamp:yyyy-MM-dd HH:mm:ss}] {ServerName}: {Data}";
        }

        public override bool Equals(object obj)
        {
            if (obj is DataRecord other)
            {
                return string.Equals(Data?.Trim(), other.Data?.Trim(), StringComparison.OrdinalIgnoreCase);
            }
            return false;
        }

        public override int GetHashCode()
        {
            return Data?.Trim()?.ToLowerInvariant()?.GetHashCode() ?? 0;
        }
    }
}
