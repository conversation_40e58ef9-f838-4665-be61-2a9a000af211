using System;

namespace TCP通讯.Infrastructure.Logging
{
    /// <summary>
    /// 日志记录器接口
    /// </summary>
    public interface ILogger
    {
        /// <summary>
        /// 记录信息日志
        /// </summary>
        void LogInfo(string message);

        /// <summary>
        /// 记录警告日志
        /// </summary>
        void LogWarning(string message);

        /// <summary>
        /// 记录错误日志
        /// </summary>
        void LogError(string message, Exception exception = null);

        /// <summary>
        /// 记录调试日志
        /// </summary>
        void LogDebug(string message);

        /// <summary>
        /// 记录详细日志
        /// </summary>
        void LogVerbose(string message);
    }

    /// <summary>
    /// 日志级别枚举
    /// </summary>
    public enum LogLevel
    {
        Verbose = 0,
        Debug = 1,
        Info = 2,
        Warning = 3,
        Error = 4
    }
}
