using System;
using System.IO;
using System.Net;
using System.Runtime.InteropServices;
using System.Text;
using TCP通讯.Core.Models;
using TCP通讯.Infrastructure.Logging;
using TCP通讯.Common;

namespace TCP通讯.Infrastructure.Configuration
{
    /// <summary>
    /// 配置迁移工具 - 从旧版INI文件迁移到新版JSON配置
    /// </summary>
    public class ConfigurationMigrator
    {
        private readonly ILogger _logger;
        private readonly string _baseDirectory;

        public ConfigurationMigrator(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
        }

        /// <summary>
        /// 执行配置迁移
        /// </summary>
        public bool MigrateConfiguration()
        {
            try
            {
                _logger.LogInfo("开始配置迁移");

                var hasLegacyConfig = CheckLegacyConfigExists();
                if (!hasLegacyConfig)
                {
                    _logger.LogInfo("未发现旧版配置文件，跳过迁移");
                    return false;
                }

                // 迁移服务器配置
                var serverConfigs = MigrateServerConfiguration();
                
                // 迁移数据过滤配置
                var filterConfig = MigrateDataFilterConfiguration();
                
                // 迁移应用程序配置
                var appConfig = MigrateApplicationConfiguration();

                // 保存新配置
                var configManager = new ConfigurationManager(_logger);
                configManager.SaveConfiguration(appConfig);
                configManager.SaveServerConfigurations(serverConfigs);
                configManager.SaveDataFilterConfiguration(filterConfig);

                // 备份旧配置文件
                BackupLegacyConfigs();

                _logger.LogInfo("配置迁移完成");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("配置迁移失败", ex);
                return false;
            }
        }

        private bool CheckLegacyConfigExists()
        {
            var ipConfigPath = Path.Combine(_baseDirectory, Constants.FilePaths.LegacyIpConfigFile);
            var appConfigPath = Path.Combine(_baseDirectory, Constants.FilePaths.LegacyAppConfigFile);
            var bianConfigPath = Path.Combine(_baseDirectory, Constants.FilePaths.LegacyBianConfigFile);

            return File.Exists(ipConfigPath) || File.Exists(appConfigPath) || File.Exists(bianConfigPath);
        }

        private TCP通讯.Core.Models.ServerConfiguration[] MigrateServerConfiguration()
        {
            var ipConfigPath = Path.Combine(_baseDirectory, Constants.FilePaths.LegacyIpConfigFile);

            var server1Config = new TCP通讯.Core.Models.ServerConfiguration
            {
                Name = Constants.UI.Server1StatusLabel,
                IPAddress = IPAddress.Any,
                Port = 8001,
                IsEnabled = false
            };

            var server2Config = new TCP通讯.Core.Models.ServerConfiguration
            {
                Name = Constants.UI.Server2StatusLabel,
                IPAddress = IPAddress.Any,
                Port = 8002,
                IsEnabled = false
            };

            if (File.Exists(ipConfigPath))
            {
                try
                {
                    // 读取扫码枪1配置
                    var ip1 = GetPrivateProfileString("连接状态", "扫码枪 1 ip", "", ipConfigPath);
                    var port1 = GetPrivateProfileString("连接状态", "扫码枪 1 端口", "", ipConfigPath);
                    
                    if (!string.IsNullOrEmpty(ip1) && IPAddress.TryParse(ip1, out var ipAddr1))
                    {
                        server1Config.IPAddress = ipAddr1;
                        server1Config.IsEnabled = true;
                    }
                    
                    if (!string.IsNullOrEmpty(port1) && int.TryParse(port1, out var portNum1))
                    {
                        server1Config.Port = portNum1;
                    }

                    // 读取扫码枪2配置
                    var ip2 = GetPrivateProfileString("连接状态", "扫码枪 2 ip", "", ipConfigPath);
                    var port2 = GetPrivateProfileString("连接状态", "扫码枪 2 端口", "", ipConfigPath);
                    
                    if (!string.IsNullOrEmpty(ip2) && IPAddress.TryParse(ip2, out var ipAddr2))
                    {
                        server2Config.IPAddress = ipAddr2;
                        server2Config.IsEnabled = true;
                    }
                    
                    if (!string.IsNullOrEmpty(port2) && int.TryParse(port2, out var portNum2))
                    {
                        server2Config.Port = portNum2;
                    }

                    _logger.LogInfo($"迁移服务器配置: {server1Config}, {server2Config}");
                }
                catch (Exception ex)
                {
                    _logger.LogError("迁移服务器配置时发生错误", ex);
                }
            }

            return new[] { server1Config, server2Config };
        }

        private DataFilterConfiguration MigrateDataFilterConfiguration()
        {
            var config = new DataFilterConfiguration();
            
            // 从config.ini迁移
            var configPath = Path.Combine(_baseDirectory, Constants.FilePaths.LegacyAppConfigFile);
            if (File.Exists(configPath))
            {
                try
                {
                    var lines = File.ReadAllLines(configPath);
                    foreach (var line in lines)
                    {
                        var parts = line.Split('=');
                        if (parts.Length != 2) continue;

                        switch (parts[0].Trim())
                        {
                            case "CheckBox1":
                                config.EnableDuplicateCheck = bool.Parse(parts[1]);
                                break;
                            case "CheckBox2":
                                config.AllowDuplicates = bool.Parse(parts[1]);
                                break;
                            case "ComboBox3":
                                if (int.TryParse(parts[1], out var interval))
                                    config.DuplicateCheckInterval = interval;
                                break;
                            case "IsLocked":
                                config.IsLocked = bool.Parse(parts[1]);
                                break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("迁移数据过滤配置时发生错误", ex);
                }
            }

            // 从Bian.ini迁移前缀配置
            var bianConfigPath = Path.Combine(_baseDirectory, Constants.FilePaths.LegacyBianConfigFile);
            if (File.Exists(bianConfigPath))
            {
                try
                {
                    var lines = File.ReadAllLines(bianConfigPath);
                    foreach (var line in lines)
                    {
                        var parts = line.Split('=');
                        if (parts.Length != 2) continue;

                        switch (parts[0].Trim())
                        {
                            case "Label":
                                config.LabelPrefix = parts[1];
                                break;
                            case "Cell":
                                config.CellPrefix = parts[1];
                                break;
                            case "PCM":
                                config.PCMPrefix = parts[1];
                                break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError("迁移Bian配置时发生错误", ex);
                }
            }

            _logger.LogInfo("数据过滤配置迁移完成");
            return config;
        }

        private AppConfiguration MigrateApplicationConfiguration()
        {
            var config = new AppConfiguration();
            
            // 设置默认值，可以根据需要从其他地方读取
            config.Application.Version = Constants.Application.Version;
            config.Application.AutoStart = false; // 需要用户重新设置
            config.Application.MinimizeToTray = true;
            config.Application.AutoConnect = false;

            config.UI.SkinFile = Constants.UI.DefaultSkinFile;
            config.UI.WindowWidth = Constants.UI.DefaultWindowWidth;
            config.UI.WindowHeight = Constants.UI.DefaultWindowHeight;

            config.Logging.LogLevel = Constants.Logging.DefaultLogLevel;
            config.Logging.EnableLogging = true;
            config.Logging.RetentionDays = Constants.Logging.DefaultRetentionDays;

            _logger.LogInfo("应用程序配置迁移完成");
            return config;
        }

        private void BackupLegacyConfigs()
        {
            try
            {
                var backupDir = Path.Combine(_baseDirectory, "Config", "Backup");
                if (!Directory.Exists(backupDir))
                {
                    Directory.CreateDirectory(backupDir);
                }

                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                
                // 备份各个配置文件
                BackupFileIfExists(Constants.FilePaths.LegacyIpConfigFile, 
                    Path.Combine(backupDir, $"ip配置_{timestamp}.ini"));
                BackupFileIfExists(Constants.FilePaths.LegacyAppConfigFile, 
                    Path.Combine(backupDir, $"config_{timestamp}.ini"));
                BackupFileIfExists(Constants.FilePaths.LegacyBianConfigFile, 
                    Path.Combine(backupDir, $"Bian_{timestamp}.ini"));

                _logger.LogInfo($"旧配置文件已备份到: {backupDir}");
            }
            catch (Exception ex)
            {
                _logger.LogError("备份旧配置文件时发生错误", ex);
            }
        }

        private void BackupFileIfExists(string sourceFileName, string backupPath)
        {
            var sourcePath = Path.Combine(_baseDirectory, sourceFileName);
            if (File.Exists(sourcePath))
            {
                File.Copy(sourcePath, backupPath, true);
                _logger.LogInfo($"备份文件: {sourceFileName} -> {Path.GetFileName(backupPath)}");
            }
        }

        #region INI File Helper Methods

        [DllImport("kernel32.dll", CharSet = CharSet.Auto)]
        private static extern int GetPrivateProfileString(string section, string key, string defaultValue, 
            StringBuilder returnValue, int size, string filePath);

        private string GetPrivateProfileString(string section, string key, string defaultValue, string filePath)
        {
            var sb = new StringBuilder(255);
            GetPrivateProfileString(section, key, defaultValue, sb, sb.Capacity, filePath);
            return sb.ToString();
        }

        #endregion
    }
}
