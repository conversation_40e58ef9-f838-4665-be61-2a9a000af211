using System;
using TCP通讯.Core.Models;

namespace TCP通讯.Infrastructure.Configuration
{
    /// <summary>
    /// 配置管理器接口
    /// </summary>
    public interface IConfigurationManager
    {
        /// <summary>
        /// 配置变更事件
        /// </summary>
        event EventHandler<ConfigurationChangedEventArgs> ConfigurationChanged;

        /// <summary>
        /// 加载应用程序配置
        /// </summary>
        AppConfiguration LoadConfiguration();

        /// <summary>
        /// 保存应用程序配置
        /// </summary>
        void SaveConfiguration(AppConfiguration config);

        /// <summary>
        /// 获取服务器配置
        /// </summary>
        TCP通讯.Core.Models.ServerConfiguration[] GetServerConfigurations();

        /// <summary>
        /// 保存服务器配置
        /// </summary>
        void SaveServerConfigurations(TCP通讯.Core.Models.ServerConfiguration[] configs);

        /// <summary>
        /// 获取数据过滤配置
        /// </summary>
        DataFilterConfiguration GetDataFilterConfiguration();

        /// <summary>
        /// 保存数据过滤配置
        /// </summary>
        void SaveDataFilterConfiguration(DataFilterConfiguration config);

        /// <summary>
        /// 重置为默认配置
        /// </summary>
        void ResetToDefault();

        /// <summary>
        /// 验证配置文件完整性
        /// </summary>
        bool ValidateConfiguration();
    }

    /// <summary>
    /// 配置变更事件参数
    /// </summary>
    public class ConfigurationChangedEventArgs : EventArgs
    {
        public string ConfigurationType { get; set; }
        public object OldValue { get; set; }
        public object NewValue { get; set; }
    }
}
