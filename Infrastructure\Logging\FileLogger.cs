using System;
using System.IO;
using System.Threading;

namespace TCP通讯.Infrastructure.Logging
{
    /// <summary>
    /// 文件日志记录器
    /// </summary>
    public class FileLogger : ILogger, IDisposable
    {
        private readonly string _logFilePath;
        private readonly LogLevel _minLogLevel;
        private readonly object _lockObject = new object();
        private readonly Timer _cleanupTimer;
        private bool _disposed = false;

        public FileLogger(string logFilePath = null, LogLevel minLogLevel = LogLevel.Info)
        {
            _logFilePath = logFilePath ?? Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs", $"tcp_log_{DateTime.Now:yyyyMMdd}.txt");
            _minLogLevel = minLogLevel;

            // 确保日志目录存在
            var logDirectory = Path.GetDirectoryName(_logFilePath);
            if (!Directory.Exists(logDirectory))
            {
                Directory.CreateDirectory(logDirectory);
            }

            // 设置定时清理旧日志文件（每天执行一次）
            _cleanupTimer = new Timer(CleanupOldLogs, null, TimeSpan.FromHours(1), TimeSpan.FromDays(1));
        }

        public void LogInfo(string message)
        {
            WriteLog(LogLevel.Info, message);
        }

        public void LogWarning(string message)
        {
            WriteLog(LogLevel.Warning, message);
        }

        public void LogError(string message, Exception exception = null)
        {
            var fullMessage = exception != null ? $"{message}\n异常详情: {exception}" : message;
            WriteLog(LogLevel.Error, fullMessage);
        }

        public void LogDebug(string message)
        {
            WriteLog(LogLevel.Debug, message);
        }

        public void LogVerbose(string message)
        {
            WriteLog(LogLevel.Verbose, message);
        }

        private void WriteLog(LogLevel level, string message)
        {
            if (level < _minLogLevel || _disposed)
                return;

            try
            {
                lock (_lockObject)
                {
                    var logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] [{level}] {message}";
                    File.AppendAllText(_logFilePath, logEntry + Environment.NewLine);
                }
            }
            catch
            {
                // 忽略日志写入错误，避免影响主程序
            }
        }

        private void CleanupOldLogs(object state)
        {
            try
            {
                var logDirectory = Path.GetDirectoryName(_logFilePath);
                if (!Directory.Exists(logDirectory))
                    return;

                var cutoffDate = DateTime.Now.AddDays(-30); // 保留30天的日志
                var logFiles = Directory.GetFiles(logDirectory, "tcp_log_*.txt");

                foreach (var file in logFiles)
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        try
                        {
                            File.Delete(file);
                        }
                        catch
                        {
                            // 忽略删除失败的文件
                        }
                    }
                }
            }
            catch
            {
                // 忽略清理过程中的错误
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _cleanupTimer?.Dispose();
                _disposed = true;
            }
        }
    }
}
