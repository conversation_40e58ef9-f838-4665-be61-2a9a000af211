using System;
using System.Collections.Concurrent;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using TCP通讯.Core.Interfaces;
using TCP通讯.Infrastructure.Logging;

namespace TCP通讯.Core.Services
{
    /// <summary>
    /// TCP服务器服务实现
    /// </summary>
    public class TcpServerService : ITcpServerService, IDisposable
    {
        private readonly ILogger _logger;
        private readonly ConcurrentDictionary<string, Socket> _servers = new ConcurrentDictionary<string, Socket>();
        private readonly ConcurrentDictionary<string, int> _connectionCounts = new ConcurrentDictionary<string, int>();
        private CancellationTokenSource _cancellationTokenSource;
        private bool _disposed = false;

        public event EventHandler<ConnectionStatusEventArgs> ConnectionStatusChanged;
        public event EventHandler<DataReceivedEventArgs> DataReceived;

        public bool IsRunning { get; private set; }
        public int ActiveConnectionCount => GetTotalConnectionCount();

        public TcpServerService(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task StartAsync(TCP通讯.Core.Models.ServerConfiguration serverConfig, CancellationToken cancellationToken = default)
        {
            if (serverConfig == null)
                throw new ArgumentNullException(nameof(serverConfig));

            if (!serverConfig.IsValid())
                throw new ArgumentException("服务器配置无效", nameof(serverConfig));

            try
            {
                _logger.LogInfo($"启动TCP服务器: {serverConfig}");

                var socket = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
                socket.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);

                var endpoint = new IPEndPoint(serverConfig.IPAddress, serverConfig.Port);
                socket.Bind(endpoint);
                socket.Listen(serverConfig.BacklogSize);

                _servers.TryAdd(serverConfig.Name, socket);
                _connectionCounts.TryAdd(serverConfig.Name, 0);

                if (_cancellationTokenSource == null)
                {
                    _cancellationTokenSource = new CancellationTokenSource();
                    IsRunning = true;
                }

                var combinedToken = CancellationTokenSource.CreateLinkedTokenSource(
                    cancellationToken, _cancellationTokenSource.Token).Token;

                OnConnectionStatusChanged(serverConfig.Name, ConnectionStatus.Waiting, "等待连接");

                // 开始异步接受连接
                _ = Task.Run(() => AcceptConnectionsAsync(socket, serverConfig, combinedToken), combinedToken);

                // 等待一小段时间确保服务器启动
                await Task.Delay(50, cancellationToken);

                _logger.LogInfo($"TCP服务器启动成功: {serverConfig}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"启动TCP服务器失败: {serverConfig}", ex);
                OnConnectionStatusChanged(serverConfig.Name, ConnectionStatus.Error, ex.Message, ex);
                throw;
            }
        }

        public async Task StopAsync()
        {
            try
            {
                _logger.LogInfo("停止TCP服务器");

                IsRunning = false;
                _cancellationTokenSource?.Cancel();

                // 等待一小段时间让正在进行的操作完成
                await Task.Delay(100);

                // 关闭所有服务器Socket
                foreach (var kvp in _servers)
                {
                    try
                    {
                        kvp.Value?.Close();
                        OnConnectionStatusChanged(kvp.Key, ConnectionStatus.Disconnected, "服务器已停止");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"关闭服务器 {kvp.Key} 时发生错误", ex);
                    }
                }

                _servers.Clear();
                _connectionCounts.Clear();

                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;

                _logger.LogInfo("TCP服务器已停止");
            }
            catch (Exception ex)
            {
                _logger.LogError("停止TCP服务器时发生错误", ex);
                throw;
            }
        }

        private async Task AcceptConnectionsAsync(Socket serverSocket, TCP通讯.Core.Models.ServerConfiguration config, CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested && IsRunning)
            {
                try
                {
                    var clientSocket = await AcceptSocketAsync(serverSocket, cancellationToken);
                    
                    if (clientSocket != null)
                    {
                        var currentCount = _connectionCounts.AddOrUpdate(config.Name, 1, (key, value) => value + 1);
                        
                        if (currentCount > config.MaxConnections)
                        {
                            _logger.LogWarning($"服务器 {config.Name} 连接数超过限制 ({config.MaxConnections})");
                            clientSocket.Close();
                            _connectionCounts.AddOrUpdate(config.Name, 0, (key, value) => Math.Max(0, value - 1));
                            continue;
                        }

                        OnConnectionStatusChanged(config.Name, ConnectionStatus.Connected, 
                            $"客户端已连接: {clientSocket.RemoteEndPoint}");

                        // 处理客户端连接
                        _ = Task.Run(() => HandleClientAsync(clientSocket, config, cancellationToken), cancellationToken);
                    }
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError($"接受连接时发生错误: {config.Name}", ex);
                    await Task.Delay(1000, cancellationToken); // 短暂延迟后重试
                }
            }
        }

        private async Task<Socket> AcceptSocketAsync(Socket serverSocket, CancellationToken cancellationToken)
        {
            try
            {
                return await Task.Factory.FromAsync(serverSocket.BeginAccept, serverSocket.EndAccept, null);
            }
            catch (ObjectDisposedException)
            {
                return null;
            }
        }

        private async Task HandleClientAsync(Socket clientSocket, TCP通讯.Core.Models.ServerConfiguration config, CancellationToken cancellationToken)
        {
            var clientEndpoint = clientSocket.RemoteEndPoint?.ToString();
            
            try
            {
                using (var networkStream = new NetworkStream(clientSocket))
                {
                    var buffer = new byte[config.BufferSize];
                    
                    while (!cancellationToken.IsCancellationRequested && clientSocket.Connected)
                    {
                        var bytesRead = await networkStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);
                        
                        if (bytesRead == 0)
                            break; // 客户端断开连接

                        var data = Encoding.ASCII.GetString(buffer, 0, bytesRead);
                        
                        OnDataReceived(config.Name, data, clientEndpoint);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // 正常取消操作
            }
            catch (Exception ex)
            {
                _logger.LogError($"处理客户端连接时发生错误: {clientEndpoint}", ex);
            }
            finally
            {
                try
                {
                    clientSocket?.Close();
                    _connectionCounts.AddOrUpdate(config.Name, 0, (key, value) => Math.Max(0, value - 1));
                    OnConnectionStatusChanged(config.Name, ConnectionStatus.Disconnected, 
                        $"客户端已断开: {clientEndpoint}");
                }
                catch (Exception ex)
                {
                    _logger.LogError($"关闭客户端连接时发生错误: {clientEndpoint}", ex);
                }
            }
        }

        private int GetTotalConnectionCount()
        {
            var total = 0;
            foreach (var count in _connectionCounts.Values)
            {
                total += count;
            }
            return total;
        }

        private void OnConnectionStatusChanged(string serverName, ConnectionStatus status, string message, Exception exception = null)
        {
            ConnectionStatusChanged?.Invoke(this, new ConnectionStatusEventArgs
            {
                ServerName = serverName,
                Status = status,
                Message = message,
                Exception = exception
            });
        }

        private void OnDataReceived(string serverName, string data, string clientEndpoint)
        {
            DataReceived?.Invoke(this, new DataReceivedEventArgs
            {
                ServerName = serverName,
                Data = data,
                Timestamp = DateTime.Now,
                ClientEndpoint = clientEndpoint
            });
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                StopAsync().Wait(5000); // 等待最多5秒
                _disposed = true;
            }
        }
    }
}
