using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using TCP通讯.Core.Interfaces;
using TCP通讯.Infrastructure.Logging;

namespace TCP通讯.Core.Services
{
    /// <summary>
    /// 性能监控服务实现
    /// </summary>
    public class PerformanceMonitor : IPerformanceMonitor, IDisposable
    {
        private readonly ILogger _logger;
        private readonly Timer _monitoringTimer;
        private readonly ConcurrentQueue<PerformanceMetrics> _metricsHistory;
        private readonly Process _currentProcess;
        private bool _isMonitoring;
        private bool _disposed;
        private PerformanceMetrics _currentMetrics;
        private DateTime _lastUpdateTime;

        public event EventHandler<PerformanceMetricsEventArgs> MetricsUpdated;

        public bool IsMonitoring => _isMonitoring;

        public PerformanceMonitor(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _metricsHistory = new ConcurrentQueue<PerformanceMetrics>();
            _currentProcess = Process.GetCurrentProcess();

            _monitoringTimer = new Timer(UpdateMetrics, null, Timeout.Infinite, Timeout.Infinite);
            _currentMetrics = new PerformanceMetrics();
            _lastUpdateTime = DateTime.Now;
        }

        public void StartMonitoring()
        {
            if (_isMonitoring)
                return;

            try
            {
                _isMonitoring = true;
                _monitoringTimer.Change(TimeSpan.Zero, TimeSpan.FromSeconds(5)); // 每5秒更新一次
                _logger.LogInfo("性能监控已启动");
            }
            catch (Exception ex)
            {
                _logger.LogError("启动性能监控失败", ex);
                throw;
            }
        }

        public void StopMonitoring()
        {
            if (!_isMonitoring)
                return;

            try
            {
                _isMonitoring = false;
                _monitoringTimer.Change(Timeout.Infinite, Timeout.Infinite);
                _logger.LogInfo("性能监控已停止");
            }
            catch (Exception ex)
            {
                _logger.LogError("停止性能监控失败", ex);
            }
        }

        public PerformanceMetrics GetCurrentMetrics()
        {
            return _currentMetrics ?? new PerformanceMetrics();
        }

        public IEnumerable<PerformanceMetrics> GetHistoricalMetrics(int minutes = 60)
        {
            var cutoffTime = DateTime.Now.AddMinutes(-minutes);
            return _metricsHistory.Where(m => m.Timestamp >= cutoffTime).OrderBy(m => m.Timestamp);
        }

        public void RecordCustomMetric(string name, double value, string unit = null)
        {
            if (string.IsNullOrEmpty(name))
                return;

            try
            {
                var metric = new CustomMetric
                {
                    Name = name,
                    Value = value,
                    Unit = unit
                };

                _currentMetrics.CustomMetrics[name] = metric;
                _logger.LogDebug($"记录自定义指标: {name} = {value} {unit}");
            }
            catch (Exception ex)
            {
                _logger.LogError($"记录自定义指标失败: {name}", ex);
            }
        }

        public void ResetMetrics()
        {
            try
            {
                while (_metricsHistory.TryDequeue(out _)) { }
                _currentMetrics = new PerformanceMetrics();
                _lastUpdateTime = DateTime.Now;

                _logger.LogInfo("性能指标已重置");
            }
            catch (Exception ex)
            {
                _logger.LogError("重置性能指标失败", ex);
            }
        }

        private void UpdateMetrics(object state)
        {
            if (!_isMonitoring || _disposed)
                return;

            try
            {
                var metrics = new PerformanceMetrics();
                var now = DateTime.Now;
                var timeDelta = (now - _lastUpdateTime).TotalSeconds;

                // 更新基本系统指标
                UpdateSystemMetrics(metrics);
                
                // 更新网络指标
                UpdateNetworkMetrics(metrics, timeDelta);
                
                // 保留自定义指标
                if (_currentMetrics?.CustomMetrics != null)
                {
                    foreach (var kvp in _currentMetrics.CustomMetrics)
                    {
                        metrics.CustomMetrics[kvp.Key] = kvp.Value;
                    }
                }

                _currentMetrics = metrics;
                _lastUpdateTime = now;

                // 添加到历史记录
                _metricsHistory.Enqueue(metrics);
                
                // 保持历史记录大小合理（最多保留24小时的数据，每5秒一条记录）
                while (_metricsHistory.Count > 17280) // 24 * 60 * 60 / 5
                {
                    _metricsHistory.TryDequeue(out _);
                }

                // 触发事件
                OnMetricsUpdated(metrics);
            }
            catch (Exception ex)
            {
                _logger.LogError("更新性能指标时发生错误", ex);
            }
        }

        private void UpdateSystemMetrics(PerformanceMetrics metrics)
        {
            try
            {
                // 简化的CPU使用率计算
                metrics.CpuUsage = 0; // 暂时设为0，避免复杂的性能计数器

                // 内存指标
                metrics.MemoryUsage = GC.GetTotalMemory(false) / 1024.0 / 1024.0; // MB
                metrics.WorkingSet = _currentProcess.WorkingSet64 / 1024.0 / 1024.0; // MB
                metrics.PrivateMemory = _currentProcess.PrivateMemorySize64 / 1024.0 / 1024.0; // MB

                // 线程和句柄
                metrics.ThreadCount = _currentProcess.Threads.Count;
                metrics.HandleCount = _currentProcess.HandleCount;
            }
            catch (Exception ex)
            {
                _logger.LogDebug($"更新系统指标时发生错误: {ex.Message}");
            }
        }

        private void UpdateNetworkMetrics(PerformanceMetrics metrics, double timeDelta)
        {
            try
            {
                // 这里可以从ApplicationController获取网络统计信息
                // 暂时使用模拟数据
                metrics.ActiveConnections = 0; // 需要从TcpServerService获取
                metrics.PacketsPerSecond = 0;
                metrics.BytesPerSecond = 0;
                metrics.AverageResponseTime = 0;
                metrics.ErrorRate = 0;
            }
            catch (Exception ex)
            {
                _logger.LogDebug($"更新网络指标时发生错误: {ex.Message}");
            }
        }

        private void OnMetricsUpdated(PerformanceMetrics metrics)
        {
            try
            {
                MetricsUpdated?.Invoke(this, new PerformanceMetricsEventArgs
                {
                    Metrics = metrics
                });
            }
            catch (Exception ex)
            {
                _logger.LogError("触发性能指标更新事件时发生错误", ex);
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                StopMonitoring();
                _monitoringTimer?.Dispose();
                _currentProcess?.Dispose();
                _disposed = true;
            }
        }
    }
}
