using System;

namespace TCP通讯.Common.Exceptions
{
    /// <summary>
    /// TCP服务器异常基类
    /// </summary>
    public class TcpServerException : Exception
    {
        public string ServerName { get; }

        public TcpServerException(string serverName) : base()
        {
            ServerName = serverName;
        }

        public TcpServerException(string serverName, string message) : base(message)
        {
            ServerName = serverName;
        }

        public TcpServerException(string serverName, string message, Exception innerException) : base(message, innerException)
        {
            ServerName = serverName;
        }
    }

    /// <summary>
    /// 服务器启动异常
    /// </summary>
    public class ServerStartException : TcpServerException
    {
        public ServerStartException(string serverName, string message) : base(serverName, message)
        {
        }

        public ServerStartException(string serverName, string message, Exception innerException) : base(serverName, message, innerException)
        {
        }
    }

    /// <summary>
    /// 服务器停止异常
    /// </summary>
    public class ServerStopException : TcpServerException
    {
        public ServerStopException(string serverName, string message) : base(serverName, message)
        {
        }

        public ServerStopException(string serverName, string message, Exception innerException) : base(serverName, message, innerException)
        {
        }
    }

    /// <summary>
    /// 配置异常
    /// </summary>
    public class ConfigurationException : Exception
    {
        public string ConfigurationType { get; }

        public ConfigurationException(string configurationType, string message) : base(message)
        {
            ConfigurationType = configurationType;
        }

        public ConfigurationException(string configurationType, string message, Exception innerException) : base(message, innerException)
        {
            ConfigurationType = configurationType;
        }
    }

    /// <summary>
    /// 数据处理异常
    /// </summary>
    public class DataProcessingException : Exception
    {
        public string ProcessingData { get; }

        public DataProcessingException(string data, string message) : base(message)
        {
            ProcessingData = data;
        }

        public DataProcessingException(string data, string message, Exception innerException) : base(message, innerException)
        {
            ProcessingData = data;
        }
    }

    /// <summary>
    /// 键盘输入异常
    /// </summary>
    public class KeyboardInputException : Exception
    {
        public string InputText { get; }

        public KeyboardInputException(string inputText, string message) : base(message)
        {
            InputText = inputText;
        }

        public KeyboardInputException(string inputText, string message, Exception innerException) : base(message, innerException)
        {
            InputText = inputText;
        }
    }
}
