using System;
using System.Threading;
using System.Windows.Forms;
using TCP通讯.Core;
using TCP通讯.Infrastructure.Configuration;
using TCP通讯.Infrastructure.Logging;
using TCP通讯.Presentation.Forms;
using TCP通讯.Common;

namespace TCP通讯
{
    /// <summary>
    /// 应用程序入口点
    /// </summary>
    static class Program
    {
        private static ILogger _logger;
        private static ApplicationController _appController;

        [STAThread]
        static void Main(string[] args)
        {
            // 初始化日志
            _logger = new FileLogger();

            try
            {
                // 检测启动模式
                var startupMode = DetectStartupMode(args);
                _logger.LogInfo($"应用程序启动 - 版本 {Constants.Application.Version} - 启动模式: {startupMode}");

                // 创建一个唯一的全局 Mutex
                bool createdNew;
                using (Mutex mutex = new Mutex(true, Constants.Application.MutexName, out createdNew))
                {
                    if (createdNew)
                    {
                        // 唯一实例
                        System.Windows.Forms.Application.EnableVisualStyles();
                        System.Windows.Forms.Application.SetCompatibleTextRenderingDefault(false);

                        // 设置全局异常处理
                        System.Windows.Forms.Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
                        System.Windows.Forms.Application.ThreadException += OnThreadException;
                        AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;

                        // 执行配置迁移
                        PerformConfigurationMigration();

                        // 初始化应用程序控制器
                        _appController = new ApplicationController();

                        // 创建并显示主窗体，传递启动模式
                        var mainForm = new MainForm(_appController, startupMode);

                        _logger.LogInfo("主窗体创建完成，启动应用程序");
                        System.Windows.Forms.Application.Run(mainForm);
                    }
                    else
                    {
                        // 已经有实例在运行
                        _logger.LogWarning("应用程序已在运行，显示警告消息");
                        MessageBox.Show(
                            Constants.ErrorMessages.ApplicationAlreadyRunning,
                            "警告",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Warning
                        );
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError("应用程序启动失败", ex);
                MessageBox.Show(
                    $"应用程序启动失败：{ex.Message}",
                    "错误",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error
                );
            }
            finally
            {
                // 清理资源
                try
                {
                    _appController?.Dispose();
                    (_logger as IDisposable)?.Dispose();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"清理资源时发生错误: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 检测启动模式
        /// </summary>
        /// <param name="args">命令行参数</param>
        /// <returns>启动模式</returns>
        private static StartupMode DetectStartupMode(string[] args)
        {
            // 检查命令行参数
            if (args != null && args.Length > 0)
            {
                foreach (var arg in args)
                {
                    if (arg.Equals("/autostart", StringComparison.OrdinalIgnoreCase) ||
                        arg.Equals("-autostart", StringComparison.OrdinalIgnoreCase) ||
                        arg.Equals("--autostart", StringComparison.OrdinalIgnoreCase))
                    {
                        return StartupMode.AutoStart;
                    }
                }
            }

            // 检查是否从启动文件夹启动
            try
            {
                var currentProcess = System.Diagnostics.Process.GetCurrentProcess();
                var processPath = currentProcess.MainModule?.FileName;
                var startupPath = Environment.GetFolderPath(Environment.SpecialFolder.Startup);

                if (!string.IsNullOrEmpty(processPath) && !string.IsNullOrEmpty(startupPath))
                {
                    // 检查是否通过启动文件夹中的快捷方式启动
                    var shortcutPath = System.IO.Path.Combine(startupPath, $"{Constants.Application.Name}.lnk");
                    if (System.IO.File.Exists(shortcutPath))
                    {
                        // 进一步检查进程启动时间，如果是系统启动后不久启动的，可能是开机自启
                        var systemUptime = Environment.TickCount;
                        var processUptime = (DateTime.Now - currentProcess.StartTime).TotalMilliseconds;

                        // 如果进程启动时间接近系统启动时间（5分钟内），认为是开机自启
                        if (systemUptime - processUptime < 300000) // 5分钟 = 300000毫秒
                        {
                            return StartupMode.AutoStart;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning($"检测启动模式失败: {ex.Message}");
            }

            return StartupMode.Manual;
        }

        /// <summary>
        /// 执行配置迁移
        /// </summary>
        private static void PerformConfigurationMigration()
        {
            try
            {
                var migrator = new ConfigurationMigrator(_logger);
                var migrated = migrator.MigrateConfiguration();

                if (migrated)
                {
                    _logger.LogInfo("配置迁移完成");
                    MessageBox.Show(
                        "检测到旧版配置文件，已自动迁移到新版本。\n旧配置文件已备份到Config/Backup目录。",
                        "配置迁移",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information
                    );
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("配置迁移失败", ex);
                MessageBox.Show(
                    $"配置迁移失败：{ex.Message}\n应用程序将使用默认配置。",
                    "配置迁移错误",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning
                );
            }
        }

        /// <summary>
        /// 处理UI线程异常
        /// </summary>
        private static void OnThreadException(object sender, ThreadExceptionEventArgs e)
        {
            _logger?.LogError("UI线程异常", e.Exception);

            var result = MessageBox.Show(
                $"应用程序发生错误：{e.Exception.Message}\n\n是否继续运行？",
                "应用程序错误",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Error
            );

            if (result == DialogResult.No)
            {
                System.Windows.Forms.Application.Exit();
            }
        }

        /// <summary>
        /// 处理非UI线程异常
        /// </summary>
        private static void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            var exception = e.ExceptionObject as Exception;
            _logger?.LogError("未处理的异常", exception);

            MessageBox.Show(
                $"应用程序发生严重错误：{exception?.Message ?? "未知错误"}\n应用程序将退出。",
                "严重错误",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error
            );

            Environment.Exit(1);
        }
    }
}
