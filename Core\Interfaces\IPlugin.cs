using System;
using System.Collections.Generic;
using TCP通讯.Infrastructure.Logging;

namespace TCP通讯.Core.Interfaces
{
    /// <summary>
    /// 插件接口
    /// </summary>
    public interface IPlugin
    {
        /// <summary>
        /// 插件名称
        /// </summary>
        string Name { get; }

        /// <summary>
        /// 插件版本
        /// </summary>
        string Version { get; }

        /// <summary>
        /// 插件描述
        /// </summary>
        string Description { get; }

        /// <summary>
        /// 插件作者
        /// </summary>
        string Author { get; }

        /// <summary>
        /// 插件是否已启用
        /// </summary>
        bool IsEnabled { get; }

        /// <summary>
        /// 插件配置
        /// </summary>
        Dictionary<string, object> Configuration { get; set; }

        /// <summary>
        /// 初始化插件
        /// </summary>
        /// <param name="context">插件上下文</param>
        void Initialize(IPluginContext context);

        /// <summary>
        /// 启动插件
        /// </summary>
        void Start();

        /// <summary>
        /// 停止插件
        /// </summary>
        void Stop();

        /// <summary>
        /// 处理数据
        /// </summary>
        /// <param name="data">输入数据</param>
        /// <returns>处理后的数据</returns>
        string ProcessData(string data);

        /// <summary>
        /// 获取插件状态
        /// </summary>
        PluginStatus GetStatus();

        /// <summary>
        /// 配置插件
        /// </summary>
        /// <param name="configuration">配置参数</param>
        void Configure(Dictionary<string, object> configuration);
    }

    /// <summary>
    /// 插件上下文接口
    /// </summary>
    public interface IPluginContext
    {
        /// <summary>
        /// 日志记录器
        /// </summary>
        ILogger Logger { get; }

        /// <summary>
        /// 应用程序控制器
        /// </summary>
        ApplicationController AppController { get; }

        /// <summary>
        /// 插件数据目录
        /// </summary>
        string DataDirectory { get; }

        /// <summary>
        /// 发送通知
        /// </summary>
        void SendNotification(string title, string message, NotificationType type = NotificationType.Info);

        /// <summary>
        /// 注册事件处理器
        /// </summary>
        void RegisterEventHandler<T>(EventHandler<T> handler) where T : EventArgs;

        /// <summary>
        /// 获取配置值
        /// </summary>
        T GetConfigValue<T>(string key, T defaultValue = default);

        /// <summary>
        /// 设置配置值
        /// </summary>
        void SetConfigValue<T>(string key, T value);
    }

    /// <summary>
    /// 插件管理器接口
    /// </summary>
    public interface IPluginManager
    {
        /// <summary>
        /// 插件加载事件
        /// </summary>
        event EventHandler<PluginEventArgs> PluginLoaded;

        /// <summary>
        /// 插件卸载事件
        /// </summary>
        event EventHandler<PluginEventArgs> PluginUnloaded;

        /// <summary>
        /// 加载插件
        /// </summary>
        /// <param name="pluginPath">插件路径</param>
        IPlugin LoadPlugin(string pluginPath);

        /// <summary>
        /// 卸载插件
        /// </summary>
        /// <param name="plugin">插件实例</param>
        void UnloadPlugin(IPlugin plugin);

        /// <summary>
        /// 获取所有已加载的插件
        /// </summary>
        IEnumerable<IPlugin> GetLoadedPlugins();

        /// <summary>
        /// 根据名称获取插件
        /// </summary>
        IPlugin GetPlugin(string name);

        /// <summary>
        /// 启用插件
        /// </summary>
        void EnablePlugin(string name);

        /// <summary>
        /// 禁用插件
        /// </summary>
        void DisablePlugin(string name);

        /// <summary>
        /// 扫描并加载插件目录中的所有插件
        /// </summary>
        void LoadPluginsFromDirectory(string directory);
    }

    /// <summary>
    /// 插件状态
    /// </summary>
    public class PluginStatus
    {
        public bool IsRunning { get; set; }
        public DateTime LastStartTime { get; set; }
        public DateTime LastStopTime { get; set; }
        public string StatusMessage { get; set; }
        public Dictionary<string, object> StatusData { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// 插件事件参数
    /// </summary>
    public class PluginEventArgs : EventArgs
    {
        public IPlugin Plugin { get; set; }
        public string Message { get; set; }
        public Exception Exception { get; set; }
    }

    /// <summary>
    /// 通知类型
    /// </summary>
    public enum NotificationType
    {
        Info,
        Warning,
        Error,
        Success
    }
}
