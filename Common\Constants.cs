namespace TCP通讯.Common
{
    /// <summary>
    /// 应用程序常量
    /// </summary>
    public static class Constants
    {
        /// <summary>
        /// 应用程序信息
        /// </summary>
        public static class Application
        {
            public const string Name = "TCP通讯";
            public const string Version = "2.0.0";
            public const string Description = "基于基恩士开发的Socket通讯模块";
            public const string Copyright = "版权所有 © 2024";
            public const string Author = "听闻远方有你";
            public const string Contact = "1302000857";
            public const string MutexName = "TCP通讯Mutex";
        }

        /// <summary>
        /// 网络配置
        /// </summary>
        public static class Network
        {
            public const int DefaultBufferSize = 1024;
            public const int DefaultBacklogSize = 10;
            public const int DefaultMaxConnections = 100;
            public const int DefaultConnectionTimeout = 30000; // 30秒
            public const int MinPort = 1;
            public const int MaxPort = 65535;
        }

        /// <summary>
        /// 文件路径
        /// </summary>
        public static class FilePaths
        {
            public const string ConfigDirectory = "Config";
            public const string LogDirectory = "Logs";
            public const string AppConfigFile = "app.json";
            public const string ServerConfigFile = "servers.json";
            public const string FilterConfigFile = "filter.json";
            
            // 兼容旧版本的配置文件
            public const string LegacyIpConfigFile = "ip配置.ini";
            public const string LegacyAppConfigFile = "config.ini";
            public const string LegacyBianConfigFile = "Bian.ini";
        }

        /// <summary>
        /// UI配置
        /// </summary>
        public static class UI
        {
            public const string DefaultSkinFile = "PageColor2.ssk";
            public const int DefaultWindowWidth = 800;
            public const int DefaultWindowHeight = 600;
            public const int MinWindowWidth = 600;
            public const int MinWindowHeight = 400;
            
            // 控件名称
            public const string Server1StatusLabel = "扫码枪1";
            public const string Server2StatusLabel = "扫码枪2";
        }

        /// <summary>
        /// 日志配置
        /// </summary>
        public static class Logging
        {
            public const string DefaultLogLevel = "Info";
            public const int DefaultRetentionDays = 30;
            public const int MaxLogFileSize = 10 * 1024 * 1024; // 10MB
            public const string LogFileNameFormat = "tcp_log_{0:yyyyMMdd}.txt";
        }

        /// <summary>
        /// 数据处理配置
        /// </summary>
        public static class DataProcessing
        {
            public const int DefaultDuplicateCheckInterval = 1; // 分钟
            public const int MaxHistoryRecords = 10000;
            public const int DefaultCleanupHours = 24;
            public const int DefaultInputDelay = 10; // 毫秒
        }

        /// <summary>
        /// 错误消息
        /// </summary>
        public static class ErrorMessages
        {
            public const string ApplicationAlreadyRunning = "应用程序已在运行。";
            public const string InvalidIPAddress = "IP地址无效。";
            public const string InvalidPort = "端口号无效。";
            public const string PortEmpty = "端口号不能为空。";
            public const string DuplicateIPAddress = "IP地址不能重复。";
            public const string ServerStartFailed = "启动监听时发生错误";
            public const string ServerNotListening = "没有在监听中。";
            public const string ServerAlreadyListening = "已经在监听中。";
            public const string ConfigurationSaveFailed = "保存配置失败";
            public const string ConfigurationLoadFailed = "加载配置失败";
        }

        /// <summary>
        /// 成功消息
        /// </summary>
        public static class SuccessMessages
        {
            public const string ConfigurationSaved = "配置已保存并锁定";
            public const string AutoStartEnabled = "设置成功";
            public const string AutoStartDisabled = "取消成功";
            public const string AutoStartNotSet = "自动重启程序未设置，请先设置程序自动重启";
        }

        /// <summary>
        /// 连接状态文本
        /// </summary>
        public static class ConnectionStatus
        {
            public const string Waiting = "等待连接";
            public const string Connected = "连接成功";
            public const string Disconnected = "连接已断开";
            public const string Error = "连接错误";
        }
    }

    /// <summary>
    /// 应用程序启动模式
    /// </summary>
    public enum StartupMode
    {
        /// <summary>
        /// 手动启动
        /// </summary>
        Manual,

        /// <summary>
        /// 开机自启动
        /// </summary>
        AutoStart
    }
}
