using System;
using System.Collections.Generic;
using TCP通讯.Core.Models;
using TCP通讯.Infrastructure.Configuration;

namespace TCP通讯.Core.Interfaces
{
    /// <summary>
    /// 数据处理服务接口
    /// </summary>
    public interface IDataProcessingService
    {
        /// <summary>
        /// 处理后的数据事件
        /// </summary>
        event EventHandler<ProcessedDataEventArgs> DataProcessed;

        /// <summary>
        /// 数据被过滤事件
        /// </summary>
        event EventHandler<DataFilteredEventArgs> DataFiltered;

        /// <summary>
        /// 处理接收到的数据
        /// </summary>
        /// <param name="dataRecord">数据记录</param>
        /// <param name="filterConfig">过滤配置</param>
        /// <returns>处理后的数据，如果被过滤则返回null</returns>
        string ProcessData(DataRecord dataRecord, TCP通讯.Infrastructure.Configuration.DataFilterConfiguration filterConfig);

        /// <summary>
        /// 检查数据是否重复
        /// </summary>
        /// <param name="data">要检查的数据</param>
        /// <param name="intervalMinutes">时间间隔（分钟）</param>
        /// <returns>如果重复返回true</returns>
        bool IsDuplicateData(string data, int intervalMinutes);

        /// <summary>
        /// 清理历史数据
        /// </summary>
        /// <param name="olderThanHours">清理多少小时前的数据</param>
        void CleanupHistory(int olderThanHours = 24);

        /// <summary>
        /// 获取数据处理统计信息
        /// </summary>
        DataProcessingStatistics GetStatistics();

        /// <summary>
        /// 重置统计信息
        /// </summary>
        void ResetStatistics();
    }

    /// <summary>
    /// 处理后的数据事件参数
    /// </summary>
    public class ProcessedDataEventArgs : EventArgs
    {
        public string OriginalData { get; set; }
        public string ProcessedData { get; set; }
        public DataRecord DataRecord { get; set; }
        public DateTime ProcessedTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 数据被过滤事件参数
    /// </summary>
    public class DataFilteredEventArgs : EventArgs
    {
        public string Data { get; set; }
        public string Reason { get; set; }
        public TimeSpan RemainingTime { get; set; }
        public DateTime FilteredTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 数据处理统计信息
    /// </summary>
    public class DataProcessingStatistics
    {
        public int TotalReceived { get; set; }
        public int TotalProcessed { get; set; }
        public int TotalFiltered { get; set; }
        public int DuplicateCount { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime LastProcessTime { get; set; }
        
        public double ProcessingRate => TotalReceived > 0 ? (double)TotalProcessed / TotalReceived * 100 : 0;
        public double FilterRate => TotalReceived > 0 ? (double)TotalFiltered / TotalReceived * 100 : 0;
    }
}
