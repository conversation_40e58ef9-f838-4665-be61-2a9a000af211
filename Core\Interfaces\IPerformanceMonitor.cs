using System;
using System.Collections.Generic;

namespace TCP通讯.Core.Interfaces
{
    /// <summary>
    /// 性能监控服务接口
    /// </summary>
    public interface IPerformanceMonitor
    {
        /// <summary>
        /// 性能指标更新事件
        /// </summary>
        event EventHandler<PerformanceMetricsEventArgs> MetricsUpdated;

        /// <summary>
        /// 开始监控
        /// </summary>
        void StartMonitoring();

        /// <summary>
        /// 停止监控
        /// </summary>
        void StopMonitoring();

        /// <summary>
        /// 获取当前性能指标
        /// </summary>
        PerformanceMetrics GetCurrentMetrics();

        /// <summary>
        /// 获取历史性能数据
        /// </summary>
        /// <param name="minutes">获取多少分钟内的数据</param>
        IEnumerable<PerformanceMetrics> GetHistoricalMetrics(int minutes = 60);

        /// <summary>
        /// 记录自定义指标
        /// </summary>
        void RecordCustomMetric(string name, double value, string unit = null);

        /// <summary>
        /// 重置所有统计数据
        /// </summary>
        void ResetMetrics();

        /// <summary>
        /// 是否正在监控
        /// </summary>
        bool IsMonitoring { get; }
    }

    /// <summary>
    /// 性能指标事件参数
    /// </summary>
    public class PerformanceMetricsEventArgs : EventArgs
    {
        public PerformanceMetrics Metrics { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 性能指标数据
    /// </summary>
    public class PerformanceMetrics
    {
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// CPU使用率 (%)
        /// </summary>
        public double CpuUsage { get; set; }

        /// <summary>
        /// 内存使用量 (MB)
        /// </summary>
        public double MemoryUsage { get; set; }

        /// <summary>
        /// 工作集内存 (MB)
        /// </summary>
        public double WorkingSet { get; set; }

        /// <summary>
        /// 私有内存 (MB)
        /// </summary>
        public double PrivateMemory { get; set; }

        /// <summary>
        /// 线程数
        /// </summary>
        public int ThreadCount { get; set; }

        /// <summary>
        /// 句柄数
        /// </summary>
        public int HandleCount { get; set; }

        /// <summary>
        /// 活动TCP连接数
        /// </summary>
        public int ActiveConnections { get; set; }

        /// <summary>
        /// 每秒处理的数据包数
        /// </summary>
        public double PacketsPerSecond { get; set; }

        /// <summary>
        /// 每秒接收的字节数
        /// </summary>
        public double BytesPerSecond { get; set; }

        /// <summary>
        /// 平均响应时间 (ms)
        /// </summary>
        public double AverageResponseTime { get; set; }

        /// <summary>
        /// 错误率 (%)
        /// </summary>
        public double ErrorRate { get; set; }

        /// <summary>
        /// 自定义指标
        /// </summary>
        public Dictionary<string, CustomMetric> CustomMetrics { get; set; } = new Dictionary<string, CustomMetric>();

        /// <summary>
        /// 获取内存使用率 (%)
        /// </summary>
        public double MemoryUsagePercentage
        {
            get
            {
                try
                {
                    // 使用GC内存信息计算使用率
                    var totalMemory = GC.GetTotalMemory(false) / 1024.0 / 1024.0; // MB
                    var workingSet = System.Diagnostics.Process.GetCurrentProcess().WorkingSet64 / 1024.0 / 1024.0; // MB

                    // 简化计算，基于工作集内存
                    return Math.Min(workingSet / 1024.0 * 100, 100); // 假设1GB为100%基准
                }
                catch
                {
                    return 0;
                }
            }
        }
    }

    /// <summary>
    /// 自定义指标
    /// </summary>
    public class CustomMetric
    {
        public string Name { get; set; }
        public double Value { get; set; }
        public string Unit { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }
}
