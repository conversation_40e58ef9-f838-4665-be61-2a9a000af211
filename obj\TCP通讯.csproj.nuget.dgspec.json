{"format": 1, "restore": {"E:\\Desktop\\TCP通讯+最新版2025217\\TCP通讯.csproj": {}}, "projects": {"E:\\Desktop\\TCP通讯+最新版2025217\\TCP通讯.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Desktop\\TCP通讯+最新版2025217\\TCP通讯.csproj", "projectName": "Socket通讯", "projectPath": "E:\\Desktop\\TCP通讯+最新版2025217\\TCP通讯.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Desktop\\TCP通讯+最新版2025217\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "fallbackFolders": ["E:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net472"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net472": {"projectReferences": {}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net472": {"dependencies": {"System.Text.Json": {"target": "Package", "version": "[6.0.0, )"}}}}, "runtimes": {"win": {"#import": []}, "win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}}}