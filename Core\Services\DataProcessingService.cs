using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using TCP通讯.Core.Interfaces;
using TCP通讯.Core.Models;
using TCP通讯.Infrastructure.Configuration;
using TCP通讯.Infrastructure.Logging;

namespace TCP通讯.Core.Services
{
    /// <summary>
    /// 数据处理服务实现
    /// </summary>
    public class DataProcessingService : IDataProcessingService
    {
        private readonly ILogger _logger;
        private readonly ConcurrentQueue<DataRecord> _dataHistory = new ConcurrentQueue<DataRecord>();
        private readonly object _statisticsLock = new object();
        private DataProcessingStatistics _statistics;

        public event EventHandler<ProcessedDataEventArgs> DataProcessed;
        public event EventHandler<DataFilteredEventArgs> DataFiltered;

        public DataProcessingService(ILogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _statistics = new DataProcessingStatistics
            {
                StartTime = DateTime.Now
            };
        }

        public string ProcessData(DataRecord dataRecord, TCP通讯.Infrastructure.Configuration.DataFilterConfiguration filterConfig)
        {
            if (dataRecord == null || string.IsNullOrWhiteSpace(dataRecord.Data))
                return null;

            try
            {
                lock (_statisticsLock)
                {
                    _statistics.TotalReceived++;
                    _statistics.LastProcessTime = DateTime.Now;
                }

                _logger.LogDebug($"处理数据: {dataRecord.Data} 来自 {dataRecord.ServerName}");

                // 检查重复数据
                if (filterConfig.EnableDuplicateCheck && !filterConfig.AllowDuplicates)
                {
                    if (IsDuplicateData(dataRecord.Data, filterConfig.DuplicateCheckInterval))
                    {
                        var lastRecord = _dataHistory.LastOrDefault(r => 
                            string.Equals(r.Data?.Trim(), dataRecord.Data?.Trim(), StringComparison.OrdinalIgnoreCase));
                        
                        if (lastRecord != null)
                        {
                            var timeSinceLastOutput = DateTime.Now - lastRecord.Timestamp;
                            var remainingTime = TimeSpan.FromMinutes(filterConfig.DuplicateCheckInterval) - timeSinceLastOutput;
                            
                            if (remainingTime > TimeSpan.Zero)
                            {
                                OnDataFiltered(dataRecord.Data, "数据重复", remainingTime);
                                
                                lock (_statisticsLock)
                                {
                                    _statistics.TotalFiltered++;
                                    _statistics.DuplicateCount++;
                                }
                                
                                return null;
                            }
                        }
                    }
                }

                // 处理数据格式
                var processedData = ProcessDataFormat(dataRecord.Data, filterConfig);
                
                if (string.IsNullOrWhiteSpace(processedData))
                {
                    OnDataFiltered(dataRecord.Data, "数据格式不匹配", TimeSpan.Zero);
                    
                    lock (_statisticsLock)
                    {
                        _statistics.TotalFiltered++;
                    }
                    
                    return null;
                }

                // 记录到历史
                _dataHistory.Enqueue(dataRecord);
                
                // 清理旧数据（保持队列大小合理）
                while (_dataHistory.Count > 10000)
                {
                    _dataHistory.TryDequeue(out _);
                }

                lock (_statisticsLock)
                {
                    _statistics.TotalProcessed++;
                }

                OnDataProcessed(dataRecord.Data, processedData, dataRecord);
                
                _logger.LogInfo($"数据处理成功: {dataRecord.Data} -> {processedData}");
                
                return processedData;
            }
            catch (Exception ex)
            {
                _logger.LogError($"处理数据时发生错误: {dataRecord.Data}", ex);
                return null;
            }
        }

        public bool IsDuplicateData(string data, int intervalMinutes)
        {
            if (string.IsNullOrWhiteSpace(data))
                return false;

            var cutoffTime = DateTime.Now.AddMinutes(-intervalMinutes);
            var trimmedData = data.Trim();

            return _dataHistory.Any(record => 
                record.Timestamp >= cutoffTime &&
                string.Equals(record.Data?.Trim(), trimmedData, StringComparison.OrdinalIgnoreCase));
        }

        public void CleanupHistory(int olderThanHours = 24)
        {
            try
            {
                var cutoffTime = DateTime.Now.AddHours(-olderThanHours);
                var tempQueue = new ConcurrentQueue<DataRecord>();
                
                while (_dataHistory.TryDequeue(out var record))
                {
                    if (record.Timestamp >= cutoffTime)
                    {
                        tempQueue.Enqueue(record);
                    }
                }

                // 重新构建队列
                while (tempQueue.TryDequeue(out var record))
                {
                    _dataHistory.Enqueue(record);
                }

                _logger.LogInfo($"清理历史数据完成，保留 {_dataHistory.Count} 条记录");
            }
            catch (Exception ex)
            {
                _logger.LogError("清理历史数据时发生错误", ex);
            }
        }

        public DataProcessingStatistics GetStatistics()
        {
            lock (_statisticsLock)
            {
                return new DataProcessingStatistics
                {
                    TotalReceived = _statistics.TotalReceived,
                    TotalProcessed = _statistics.TotalProcessed,
                    TotalFiltered = _statistics.TotalFiltered,
                    DuplicateCount = _statistics.DuplicateCount,
                    StartTime = _statistics.StartTime,
                    LastProcessTime = _statistics.LastProcessTime
                };
            }
        }

        public void ResetStatistics()
        {
            lock (_statisticsLock)
            {
                _statistics = new DataProcessingStatistics
                {
                    StartTime = DateTime.Now
                };
            }
            
            _logger.LogInfo("数据处理统计信息已重置");
        }

        private string ProcessDataFormat(string rawData, TCP通讯.Infrastructure.Configuration.DataFilterConfiguration filterConfig)
        {
            if (string.IsNullOrWhiteSpace(rawData))
                return null;

            try
            {
                // 分割数据行
                var lines = rawData.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                
                if (lines.Length == 0)
                    return null;

                var result = new StringBuilder();
                var hasValidData = false;

                // 处理标签前缀
                if (!string.IsNullOrEmpty(filterConfig.LabelPrefix))
                {
                    foreach (var line in lines)
                    {
                        if (line.StartsWith(filterConfig.LabelPrefix, StringComparison.OrdinalIgnoreCase))
                        {
                            result.AppendLine(line);
                            hasValidData = true;
                            break; // 只取第一个匹配的
                        }
                    }
                }

                // 处理单元格前缀
                if (!string.IsNullOrEmpty(filterConfig.CellPrefix))
                {
                    foreach (var line in lines)
                    {
                        if (line.StartsWith(filterConfig.CellPrefix, StringComparison.OrdinalIgnoreCase))
                        {
                            result.AppendLine(line);
                            hasValidData = true;
                            break; // 只取第一个匹配的
                        }
                    }
                }

                // 处理PCM前缀
                if (!string.IsNullOrEmpty(filterConfig.PCMPrefix))
                {
                    foreach (var line in lines)
                    {
                        if (line.StartsWith(filterConfig.PCMPrefix, StringComparison.OrdinalIgnoreCase))
                        {
                            result.AppendLine(line);
                            hasValidData = true;
                            break; // 只取第一个匹配的
                        }
                    }
                }

                // 如果没有配置前缀或没有匹配的数据，返回原始数据
                if (!hasValidData)
                {
                    if (string.IsNullOrEmpty(filterConfig.LabelPrefix) && 
                        string.IsNullOrEmpty(filterConfig.CellPrefix) && 
                        string.IsNullOrEmpty(filterConfig.PCMPrefix))
                    {
                        return rawData.ToUpperInvariant(); // 转换为大写
                    }
                    return null; // 有配置但没有匹配的数据
                }

                var processedData = result.ToString().Trim().ToUpperInvariant();
                
                // 确保以换行符结尾
                if (!processedData.EndsWith("\n"))
                {
                    processedData += "\n";
                }

                return processedData;
            }
            catch (Exception ex)
            {
                _logger.LogError($"处理数据格式时发生错误: {rawData}", ex);
                return null;
            }
        }

        private void OnDataProcessed(string originalData, string processedData, DataRecord dataRecord)
        {
            DataProcessed?.Invoke(this, new ProcessedDataEventArgs
            {
                OriginalData = originalData,
                ProcessedData = processedData,
                DataRecord = dataRecord
            });
        }

        private void OnDataFiltered(string data, string reason, TimeSpan remainingTime)
        {
            DataFiltered?.Invoke(this, new DataFilteredEventArgs
            {
                Data = data,
                Reason = reason,
                RemainingTime = remainingTime
            });
        }
    }
}
